import request from './request';

export const oauth2 = {
  // 获取授权URL
  getAuthorizationUrl(params) {
    const query = new URLSearchParams(params).toString();
    return `/oauth2/authorize?${query}`;
  },

  // 处理授权
  async authorize(params) {
    const response = await request.post('/api/oauth2/authorize', params);
    return response.data;
  },

  // 处理授权回调
  async handleCallback(code, state) {
    const response = await request.post('/api/oauth2/callback', { code, state });
    return response.data;
  },

  // 获取访问令牌
  async getAccessToken(params) {
    const response = await request.post('/api/oauth2/token', params);
    return response.data;
  },

  // 刷新令牌
  async refreshToken(refreshToken) {
    const response = await request.post('/api/oauth2/token', {
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
    });
    return response.data;
  },

  // 撤销令牌
  async revokeToken(token) {
    await request.post('/api/oauth2/revoke', { token });
  },
};
