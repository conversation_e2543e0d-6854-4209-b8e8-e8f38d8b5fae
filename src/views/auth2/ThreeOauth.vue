<template>
  <div class="oauth-login-container">
    <div class="login-card">
      <!-- 应用信息头部 -->
      <div class="app-header">
        <div class="app-info">
          <img :src="appInfo.icon" alt="App Icon" class="app-icon" />
          <div class="app-details">
            <h2>{{ appInfo.name }}</h2>
            <p>{{ appInfo.description }}</p>
          </div>
        </div>
        <div class="auth-notice">
          <i class="el-icon-info"></i>
          <span>想要访问您的账户</span>
        </div>
      </div>

      <!-- 登录表单 -->
      <div v-if="!isLoggedIn" class="login-form">
        <h3>登录以继续</h3>
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名/邮箱"
              prefix-icon="el-icon-user"
              size="large"
            >
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              prefix-icon="el-icon-lock"
              size="large"
              show-password
            >
            </el-input>
          </el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loginLoading"
            class="login-btn"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form>

        <div class="login-footer">
          <el-link type="primary" @click="showRegister = true">注册账户</el-link>
          <el-link type="primary" @click="showForgotPassword = true">忘记密码?</el-link>
        </div>
      </div>

      <!-- 授权确认 -->
      <div v-else class="auth-confirm">
        <div class="user-info">
          <el-avatar :size="60" :src="userInfo.avatar">{{ userInfo.name.charAt(0) }}</el-avatar>
          <div class="user-details">
            <h3>{{ userInfo.name }}</h3>
            <p>{{ userInfo.email }}</p>
            <el-link type="primary" @click="switchAccount">切换账户</el-link>
          </div>
        </div>

        <div class="permissions">
          <h4>{{ appInfo.name }} 将获得以下权限：</h4>
          <ul class="permission-list">
            <li v-for="scope in requestedScopes" :key="scope.name">
              <i :class="scope.icon"></i>
              <span>{{ scope.description }}</span>
            </li>
          </ul>
        </div>

        <div class="auth-actions">
          <el-button size="large" @click="handleCancel">取消</el-button>
          <el-button type="primary" size="large" :loading="authLoading" @click="handleAuthorize">
            授权
          </el-button>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="footer-info">
        <p>
          继续操作即表示您同意我们的 <el-link type="primary">服务条款</el-link> 和
          <el-link type="primary">隐私政策</el-link>
        </p>
      </div>
    </div>

    <!-- 注册对话框 -->
    <el-dialog
      title="注册账户"
      :visible.sync="showRegister"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form ref="registerForm" :model="registerForm" :rules="registerRules" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="registerForm.username"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="registerForm.email"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="registerForm.password" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="registerForm.confirmPassword" type="password" show-password></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showRegister = false">取消</el-button>
        <el-button type="primary" :loading="registerLoading" @click="handleRegister"
          >注册</el-button
        >
      </div>
    </el-dialog>

    <!-- 忘记密码对话框 -->
    <el-dialog
      title="重置密码"
      :visible.sync="showForgotPassword"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form ref="resetForm" :model="resetForm" :rules="resetRules" label-width="80px">
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="resetForm.email" placeholder="请输入注册邮箱"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showForgotPassword = false">取消</el-button>
        <el-button type="primary" :loading="resetLoading" @click="handleResetPassword"
          >发送重置邮件</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'OAuthLogin',
  data() {
    return {
      // 应用信息
      appInfo: {
        name: 'TodoMaster',
        description: '高效的任务管理应用',
        icon: 'https://via.placeholder.com/60x60/4A90E2/ffffff?text=TM',
      },

      // 用户信息
      userInfo: {
        name: '张三',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/60x60/67C23A/ffffff?text=张',
      },

      // 权限范围
      requestedScopes: [
        { name: 'profile', description: '查看您的基本信息', icon: 'el-icon-user' },
        { name: 'email', description: '访问您的邮箱地址', icon: 'el-icon-message' },
        { name: 'tasks', description: '管理您的任务列表', icon: 'el-icon-s-order' },
      ],

      // 登录状态
      isLoggedIn: false,
      loginLoading: false,
      authLoading: false,

      // 登录表单
      loginForm: {
        username: '',
        password: '',
      },

      loginRules: {
        username: [{ required: true, message: '请输入用户名或邮箱', trigger: 'blur' }],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
        ],
      },

      // 注册表单
      showRegister: false,
      registerLoading: false,
      registerForm: {
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
      },

      registerRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' },
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' },
        ],
      },

      // 重置密码表单
      showForgotPassword: false,
      resetLoading: false,
      resetForm: {
        email: '',
      },

      resetRules: {
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
        ],
      },
    };
  },

  mounted() {
    this.initPage();
  },

  methods: {
    // 初始化页面
    initPage() {
      const urlParams = new URLSearchParams(window.location.search);
      const clientId = urlParams.get('client_id');
      const redirectUri = urlParams.get('redirect_uri');
      const scope = urlParams.get('scope');
      const state = urlParams.get('state');

      if (!clientId || !redirectUri) {
        this.$message.error('缺少必要参数');
        return;
      }

      // 检查用户是否已登录
      this.checkLoginStatus();

      // 根据应用ID获取应用信息
      this.loadAppInfo(clientId);
    },

    // 检查登录状态
    checkLoginStatus() {
      const token = localStorage.getItem('access_token');
      if (token) {
        this.isLoggedIn = true;
        this.loadUserInfo();
      }
    },

    // 加载应用信息
    loadAppInfo(clientId) {
      // 模拟API调用
      console.log('Loading app info for client:', clientId);
    },

    // 加载用户信息
    loadUserInfo() {
      // 模拟API调用
      console.log('Loading user info');
    },

    // 处理登录
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loginLoading = true;

          // 模拟登录API调用
          setTimeout(() => {
            this.loginLoading = false;
            this.isLoggedIn = true;
            this.$message.success('登录成功');

            // 保存token
            localStorage.setItem('access_token', 'mock_token_' + Date.now());
          }, 1000);
        }
      });
    },

    // 处理授权
    handleAuthorize() {
      this.authLoading = true;

      // 模拟授权API调用
      setTimeout(() => {
        this.authLoading = false;

        // 生成授权码
        const authCode = 'auth_code_' + Math.random().toString(36).substr(2, 9);

        // 构建重定向URL
        const urlParams = new URLSearchParams(window.location.search);
        const redirectUri = urlParams.get('redirect_uri');
        const state = urlParams.get('state');

        let redirectUrl = `${redirectUri}?code=${authCode}`;
        if (state) {
          redirectUrl += `&state=${state}`;
        }

        // 重定向到应用
        window.location.href = redirectUrl;
      }, 1000);
    },

    // 取消授权
    handleCancel() {
      const urlParams = new URLSearchParams(window.location.search);
      const redirectUri = urlParams.get('redirect_uri');
      const state = urlParams.get('state');

      let redirectUrl = `${redirectUri}?error=access_denied`;
      if (state) {
        redirectUrl += `&state=${state}`;
      }

      window.location.href = redirectUrl;
    },

    // 切换账户
    switchAccount() {
      this.isLoggedIn = false;
      localStorage.removeItem('access_token');
      this.loginForm = { username: '', password: '' };
    },

    // 处理注册
    handleRegister() {
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          this.registerLoading = true;

          // 模拟注册API调用
          setTimeout(() => {
            this.registerLoading = false;
            this.showRegister = false;
            this.$message.success('注册成功，请登录');

            // 清空表单
            this.registerForm = {
              username: '',
              email: '',
              password: '',
              confirmPassword: '',
            };
          }, 1000);
        }
      });
    },

    // 处理重置密码
    handleResetPassword() {
      this.$refs.resetForm.validate((valid) => {
        if (valid) {
          this.resetLoading = true;

          // 模拟重置密码API调用
          setTimeout(() => {
            this.resetLoading = false;
            this.showForgotPassword = false;
            this.$message.success('重置邮件已发送');

            // 清空表单
            this.resetForm = { email: '' };
          }, 1000);
        }
      });
    },

    // 验证确认密码
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.registerForm.password) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    },
  },
};
</script>

<style scoped>
.oauth-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 420px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 应用信息头部 */
.app-header {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.app-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
}

.app-details h2 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.app-details p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.auth-notice {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 14px;
}

/* 登录表单 */
.login-form {
  padding: 24px;
}

.login-form h3 {
  margin: 0 0 24px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}

.login-form >>> .el-form-item {
  margin-bottom: 20px;
}

.login-btn {
  width: 100%;
  margin-top: 8px;
}

.login-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 授权确认 */
.auth-confirm {
  padding: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.user-details h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.user-details p {
  margin: 0 0 4px 0;
  color: #606266;
  font-size: 14px;
}

.permissions {
  margin-bottom: 24px;
}

.permissions h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.permission-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.permission-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  color: #606266;
  font-size: 14px;
}

.permission-list li i {
  color: #409eff;
  width: 16px;
}

.auth-actions {
  display: flex;
  gap: 12px;
}

.auth-actions .el-button {
  flex: 1;
}

/* 底部信息 */
.footer-info {
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #f0f0f0;
}

.footer-info p {
  margin: 0;
  color: #909399;
  font-size: 12px;
  text-align: center;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .oauth-login-container {
    padding: 10px;
  }

  .login-card {
    max-width: 100%;
  }

  .app-header {
    padding: 20px;
  }

  .login-form {
    padding: 20px;
  }

  .auth-confirm {
    padding: 20px;
  }

  .user-info {
    flex-direction: column;
    text-align: center;
  }

  .auth-actions {
    flex-direction: column;
  }

  .login-footer {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* 动画效果 */
.login-card {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
