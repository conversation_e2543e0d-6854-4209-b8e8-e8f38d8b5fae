<template>
  <div class="oauth-container">
    <!-- 顶部导航 -->
    <el-header class="header">
      <div class="header-content">
        <div class="header-left">
          <div class="logo">
            <i class="el-icon-key"></i>
            <span>OAuth2.0 Center</span>
          </div>
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item>控制台</el-breadcrumb-item>
            <el-breadcrumb-item>{{ getMenuName(activeMenu) }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <!-- 主题切换 -->
          <el-tooltip content="切换主题" placement="bottom">
            <el-button
              circle
              size="small"
              :icon="isDark ? 'el-icon-sunny' : 'el-icon-moon'"
              class="theme-toggle"
              @click="toggleTheme"
            ></el-button>
          </el-tooltip>

          <!-- 通知 -->
          <el-badge :value="notifications.length" class="notification-badge">
            <el-dropdown @command="handleNotification">
              <el-button
                circle
                size="small"
                icon="el-icon-bell"
                class="notification-btn"
              ></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="notification in notifications"
                  :key="notification.id"
                  :command="notification.id"
                >
                  <div class="notification-item">
                    <i :class="notification.icon"></i>
                    <span>{{ notification.message }}</span>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-badge>

          <!-- 用户信息 -->
          <el-dropdown class="user-dropdown" @command="handleCommand">
            <span class="el-dropdown-link">
              <el-avatar :size="36" :src="userInfo.avatar" class="user-avatar"></el-avatar>
              <div class="user-info-text">
                <div class="username">{{ userInfo.username }}</div>
                <div class="user-role">{{ userInfo.role }}</div>
              </div>
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">
                <i class="el-icon-user"></i>个人信息
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <i class="el-icon-setting"></i>系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <i class="el-icon-switch-button"></i>退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <el-container class="main-container">
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="sidebar" :class="{ 'sidebar-collapsed': isCollapsed }">
        <div class="sidebar-header">
          <el-button
            circle
            size="small"
            :icon="isCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
            class="collapse-btn"
            @click="toggleSidebar"
          ></el-button>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          :collapse="isCollapsed"
          background-color="#001529"
          text-color="#ffffff"
          active-text-color="#1890ff"
          @select="handleMenuSelect"
        >
          <el-menu-item index="dashboard" class="menu-item">
            <i class="el-icon-s-home"></i>
            <span slot="title">仪表盘</span>
          </el-menu-item>
          <el-menu-item index="applications" class="menu-item">
            <i class="el-icon-s-grid"></i>
            <span slot="title">应用管理</span>
          </el-menu-item>
          <el-menu-item index="tokens" class="menu-item">
            <i class="el-icon-key"></i>
            <span slot="title">令牌管理</span>
          </el-menu-item>
          <el-menu-item index="scopes" class="menu-item">
            <i class="el-icon-s-check"></i>
            <span slot="title">权限范围</span>
          </el-menu-item>
          <el-menu-item index="authorize" class="menu-item">
            <i class="el-icon-unlock"></i>
            <span slot="title">授权中心</span>
          </el-menu-item>
          <el-menu-item index="logs" class="menu-item">
            <i class="el-icon-document"></i>
            <span slot="title">操作日志</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="main-content">
        <!-- 仪表盘 -->
        <div v-if="activeMenu === 'dashboard'" class="dashboard">
          <div class="page-header">
            <h2>仪表盘</h2>
            <div class="page-actions">
              <el-button type="primary" icon="el-icon-refresh" @click="refreshData"
                >刷新数据</el-button
              >
            </div>
          </div>

          <!-- 统计卡片 -->
          <el-row :gutter="20" class="stats-row">
            <el-col :span="6">
              <el-card class="stat-card blue">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.totalApplications }}</div>
                  <div class="stat-label">注册应用</div>
                  <div class="stat-trend">
                    <i class="el-icon-top trend-up"></i>
                    <span>+12%</span>
                  </div>
                </div>
                <div class="stat-icon">
                  <i class="el-icon-s-grid"></i>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card green">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.activeTokens }}</div>
                  <div class="stat-label">活跃令牌</div>
                  <div class="stat-trend">
                    <i class="el-icon-top trend-up"></i>
                    <span>+8%</span>
                  </div>
                </div>
                <div class="stat-icon">
                  <i class="el-icon-key"></i>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card orange">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.dailyAuth }}</div>
                  <div class="stat-label">今日授权</div>
                  <div class="stat-trend">
                    <i class="el-icon-bottom trend-down"></i>
                    <span>-5%</span>
                  </div>
                </div>
                <div class="stat-icon">
                  <i class="el-icon-unlock"></i>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card red">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.expiredTokens }}</div>
                  <div class="stat-label">过期令牌</div>
                  <div class="stat-trend">
                    <i class="el-icon-bottom trend-down"></i>
                    <span>-3%</span>
                  </div>
                </div>
                <div class="stat-icon">
                  <i class="el-icon-warning"></i>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 图表区域 -->
          <el-row :gutter="20" class="charts-row">
            <el-col :span="16">
              <el-card class="chart-card">
                <div slot="header" class="chart-header">
                  <span>授权趋势</span>
                  <el-button-group class="chart-controls">
                    <el-button
                      size="mini"
                      :type="chartPeriod === '7d' ? 'primary' : ''"
                      @click="chartPeriod = '7d'"
                      >7天</el-button
                    >
                    <el-button
                      size="mini"
                      :type="chartPeriod === '30d' ? 'primary' : ''"
                      @click="chartPeriod = '30d'"
                      >30天</el-button
                    >
                    <el-button
                      size="mini"
                      :type="chartPeriod === '90d' ? 'primary' : ''"
                      @click="chartPeriod = '90d'"
                      >90天</el-button
                    >
                  </el-button-group>
                </div>
                <div class="chart-content">
                  <div class="chart-placeholder">
                    <i class="el-icon-s-data"></i>
                    <p>授权趋势图表</p>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="chart-card">
                <div slot="header">
                  <span>应用分布</span>
                </div>
                <div class="chart-content">
                  <div class="chart-placeholder">
                    <i class="el-icon-pie-chart"></i>
                    <p>应用类型分布</p>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 快速操作和最近活动 -->
          <el-row :gutter="20" class="bottom-row">
            <el-col :span="12">
              <el-card class="quick-actions-card">
                <div slot="header">
                  <i class="el-icon-lightning"></i>
                  <span>快速操作</span>
                </div>
                <div class="quick-actions">
                  <el-button type="primary" icon="el-icon-plus" @click="activeMenu = 'applications'"
                    >创建应用</el-button
                  >
                  <el-button type="success" icon="el-icon-unlock" @click="activeMenu = 'authorize'"
                    >授权测试</el-button
                  >
                  <el-button type="info" icon="el-icon-view" @click="activeMenu = 'tokens'"
                    >查看令牌</el-button
                  >
                  <el-button type="warning" icon="el-icon-setting" @click="activeMenu = 'scopes'"
                    >权限设置</el-button
                  >
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="recent-activity-card">
                <div slot="header">
                  <i class="el-icon-time"></i>
                  <span>最近活动</span>
                </div>
                <el-timeline class="activity-timeline">
                  <el-timeline-item
                    v-for="activity in recentActivities"
                    :key="activity.id"
                    :timestamp="activity.timestamp"
                    :type="activity.type"
                  >
                    <div class="activity-content">
                      <div class="activity-title">{{ activity.title }}</div>
                      <div class="activity-description">{{ activity.description }}</div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 应用管理 -->
        <div v-else-if="activeMenu === 'applications'" class="applications">
          <div class="page-header">
            <h2>应用管理</h2>
            <div class="page-actions">
              <el-input
                v-model="searchText"
                placeholder="搜索应用..."
                class="search-input"
                prefix-icon="el-icon-search"
                clearable
              ></el-input>
              <el-button type="primary" icon="el-icon-plus" @click="showCreateApp = true"
                >创建应用</el-button
              >
            </div>
          </div>

          <div class="filter-bar">
            <el-form :model="filters" inline>
              <el-form-item label="应用类型">
                <el-select v-model="filters.type" placeholder="选择类型" clearable>
                  <el-option label="Web应用" value="web"></el-option>
                  <el-option label="移动应用" value="mobile"></el-option>
                  <el-option label="桌面应用" value="desktop"></el-option>
                  <el-option label="服务应用" value="service"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="filters.status" placeholder="选择状态" clearable>
                  <el-option label="启用" value="active"></el-option>
                  <el-option label="禁用" value="inactive"></el-option>
                  <el-option label="待审核" value="pending"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button @click="resetFilters">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <div class="app-grid">
            <el-row :gutter="20">
              <el-col
                v-for="app in filteredApplications"
                :key="app.id"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
                class="app-col"
              >
                <el-card class="app-card" shadow="hover">
                  <div class="app-card-header">
                    <div class="app-basic-info">
                      <el-avatar :size="48" :src="app.icon" class="app-avatar">
                        <i class="el-icon-s-grid"></i>
                      </el-avatar>
                      <div class="app-title-section">
                        <h4 class="app-name">{{ app.name }}</h4>
                        <p class="app-description">{{ app.description }}</p>
                      </div>
                    </div>
                    <el-dropdown
                      trigger="click"
                      class="app-dropdown"
                      @command="(command) => handleAppAction(command, app)"
                    >
                      <el-button
                        type="text"
                        icon="el-icon-more"
                        size="small"
                        class="more-btn"
                      ></el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="edit" icon="el-icon-edit">编辑</el-dropdown-item>
                        <el-dropdown-item command="view" icon="el-icon-view"
                          >查看详情</el-dropdown-item
                        >
                        <el-dropdown-item command="reset" icon="el-icon-refresh"
                          >重置密钥</el-dropdown-item
                        >
                        <el-dropdown-item command="delete" icon="el-icon-delete" divided
                          >删除</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>

                  <div class="app-card-body">
                    <div class="app-info-grid">
                      <div class="info-item">
                        <span class="info-label">应用ID</span>
                        <span class="info-value">{{ app.clientId }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">类型</span>
                        <el-tag :type="getAppTypeTag(app.type)" size="small">{{
                          app.type || 'web'
                        }}</el-tag>
                      </div>
                      <div class="info-item">
                        <span class="info-label">状态</span>
                        <el-tag :type="getStatusTag(app.status)" size="small">
                          {{ app.status === 'active' ? '启用' : '禁用' }}
                        </el-tag>
                      </div>
                      <div class="info-item">
                        <span class="info-label">创建时间</span>
                        <span class="info-value">{{ app.createdAt }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="app-card-footer">
                    <el-button-group class="app-actions">
                      <el-button size="small" icon="el-icon-view" @click="viewApp(app)"
                        >查看</el-button
                      >
                      <el-button size="small" icon="el-icon-edit" @click="editApp(app)"
                        >编辑</el-button
                      >
                      <el-button
                        size="small"
                        type="danger"
                        icon="el-icon-delete"
                        @click="deleteApp(app)"
                        >删除</el-button
                      >
                    </el-button-group>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 令牌管理 -->
        <div v-else-if="activeMenu === 'tokens'" class="tokens">
          <div class="page-header">
            <h2>令牌管理</h2>
            <div class="page-actions">
              <el-button type="success" icon="el-icon-refresh" @click="refreshAllTokens"
                >刷新所有令牌</el-button
              >
              <el-button type="danger" icon="el-icon-delete" @click="cleanupExpiredTokens"
                >清理过期令牌</el-button
              >
            </div>
          </div>

          <div class="token-overview">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card class="token-summary-card active" shadow="hover">
                  <div class="token-summary">
                    <div class="summary-icon">
                      <i class="el-icon-key"></i>
                    </div>
                    <div class="summary-content">
                      <div class="summary-number">{{ activeTokensCount }}</div>
                      <div class="summary-label">活跃令牌</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="token-summary-card expired" shadow="hover">
                  <div class="token-summary">
                    <div class="summary-icon">
                      <i class="el-icon-warning"></i>
                    </div>
                    <div class="summary-content">
                      <div class="summary-number">{{ expiredTokensCount }}</div>
                      <div class="summary-label">过期令牌</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="token-summary-card revoked" shadow="hover">
                  <div class="token-summary">
                    <div class="summary-icon">
                      <i class="el-icon-circle-close"></i>
                    </div>
                    <div class="summary-content">
                      <div class="summary-number">{{ revokedTokensCount }}</div>
                      <div class="summary-label">已撤销令牌</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <el-card class="token-table-card">
            <div slot="header" class="table-header">
              <span>令牌列表</span>
              <div class="table-actions">
                <el-input
                  v-model="tokenSearchText"
                  placeholder="搜索令牌..."
                  prefix-icon="el-icon-search"
                  size="small"
                  clearable
                ></el-input>
              </div>
            </div>

            <el-table
              v-loading="loading"
              :data="filteredTokens"
              style="width: 100%"
              :row-class-name="tableRowClassName"
            >
              <el-table-column prop="id" label="令牌ID" width="120">
                <template slot-scope="scope">
                  <el-tag size="mini">{{ scope.row.id }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="appName" label="应用名称" width="150"></el-table-column>
              <el-table-column prop="type" label="类型" width="100">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.type === 'access' ? 'primary' : 'success'" size="mini">
                    {{ scope.row.type === 'access' ? '访问令牌' : '刷新令牌' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="scopes" label="权限范围" width="200">
                <template slot-scope="scope">
                  <el-tag
                    v-for="scopeItem in scope.row.scopes"
                    :key="scopeItem"
                    size="mini"
                    class="scope-tag"
                  >
                    {{ scopeItem }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  <el-tag :type="getTokenStatusTag(scope.row.status)" size="mini">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="expiresAt" label="过期时间" width="160">
                <template slot-scope="scope">
                  <div class="expires-info">
                    <span>{{ scope.row.expiresAt }}</span>
                    <el-progress
                      :percentage="getTokenProgress(scope.row)"
                      :status="getTokenProgressStatus(scope.row)"
                      :show-text="false"
                      :stroke-width="4"
                      class="token-progress"
                    ></el-progress>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="创建时间" width="160"></el-table-column>
              <el-table-column label="操作" width="180">
                <template slot-scope="scope">
                  <el-button-group>
                    <el-button
                      size="mini"
                      type="primary"
                      icon="el-icon-view"
                      @click="viewTokenDetails(scope.row)"
                    ></el-button>
                    <el-button
                      size="mini"
                      type="success"
                      icon="el-icon-refresh"
                      :disabled="scope.row.status === 'revoked'"
                      @click="refreshToken(scope.row)"
                    ></el-button>
                    <el-button
                      size="mini"
                      type="info"
                      icon="el-icon-copy-document"
                      @click="copyToken(scope.row.token)"
                    ></el-button>
                    <el-button
                      size="mini"
                      type="danger"
                      icon="el-icon-delete"
                      :disabled="scope.row.status === 'revoked'"
                      @click="revokeToken(scope.row)"
                    ></el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>

        <!-- 权限范围管理 -->
        <div v-else-if="activeMenu === 'scopes'" class="scopes">
          <div class="page-header">
            <h2>权限范围管理</h2>
            <div class="page-actions">
              <el-button type="primary" icon="el-icon-plus" @click="showCreateScope = true"
                >创建权限范围</el-button
              >
            </div>
          </div>

          <div class="scope-categories">
            <el-row :gutter="20">
              <el-col v-for="category in scopeCategories" :key="category.name" :span="8">
                <el-card class="scope-category-card">
                  <div slot="header" class="category-header">
                    <i :class="category.icon"></i>
                    <span>{{ category.name }}</span>
                  </div>
                  <div class="scope-list">
                    <div v-for="scope in category.scopes" :key="scope.id" class="scope-item">
                      <div class="scope-info">
                        <div class="scope-name">{{ scope.name }}</div>
                        <div class="scope-description">{{ scope.description }}</div>
                      </div>
                      <div class="scope-actions">
                        <el-tag :type="getScopeLevelType(scope.level)" size="mini">
                          {{ scope.level }}
                        </el-tag>
                        <el-dropdown @command="(command) => handleScopeAction(command, scope)">
                          <el-button type="text" icon="el-icon-more" size="mini"></el-button>
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="edit">编辑</el-dropdown-item>
                            <el-dropdown-item command="delete">删除</el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 授权中心 -->
        <div v-else-if="activeMenu === 'authorize'" class="authorize">
          <div class="page-header">
            <h2>授权中心</h2>
            <div class="page-actions">
              <el-button @click="resetAuthForm">重置表单</el-button>
            </div>
          </div>

          <el-row :gutter="24">
            <el-col :span="14">
              <el-card class="auth-form-card" shadow="never">
                <div slot="header" class="auth-card-header">
                  <div class="auth-header-title">
                    <i class="el-icon-unlock"></i>
                    <span>OAuth2.0 授权</span>
                  </div>
                </div>
                <el-form ref="authForm" :model="authForm" :rules="authRules" label-width="120px">
                  <el-form-item label="应用选择" prop="clientId">
                    <el-select
                      v-model="authForm.clientId"
                      placeholder="选择应用"
                      filterable
                      style="width: 100%"
                    >
                      <el-option
                        v-for="app in applications"
                        :key="app.id"
                        :label="app.name"
                        :value="app.clientId"
                      >
                        <span style="float: left">{{ app.name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{
                          app.clientId
                        }}</span>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="回调地址" prop="redirectUri">
                    <el-input
                      v-model="authForm.redirectUri"
                      placeholder="http://localhost:3000/callback"
                    ></el-input>
                  </el-form-item>

                  <el-form-item label="权限范围" prop="scopes">
                    <el-checkbox-group v-model="authForm.scopes">
                      <el-checkbox
                        v-for="scope in availableScopes"
                        :key="scope.id"
                        :label="scope.name"
                        class="scope-checkbox"
                      >
                        <div class="scope-checkbox-content">
                          <div class="scope-checkbox-title">{{ scope.name }}</div>
                          <div class="scope-checkbox-desc">{{ scope.description }}</div>
                        </div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>

                  <el-form-item label="响应类型" prop="responseType">
                    <el-radio-group v-model="authForm.responseType">
                      <el-radio label="code">授权码模式</el-radio>
                      <el-radio label="token">简化模式</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item label="状态参数">
                    <el-input v-model="authForm.state" placeholder="可选的状态参数"></el-input>
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" :loading="authLoading" @click="submitAuth">
                      发起授权
                    </el-button>
                    <el-button @click="resetAuthForm">重置</el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </el-col>

            <el-col :span="10">
              <el-card class="auth-flow-card" shadow="never">
                <div slot="header" class="auth-card-header">
                  <div class="auth-header-title">
                    <i class="el-icon-connection"></i>
                    <span>授权流程</span>
                  </div>
                </div>
                <el-steps :active="currentStep" direction="vertical" class="auth-steps">
                  <el-step title="验证客户端" description="验证应用身份和回调地址"></el-step>
                  <el-step title="用户授权" description="用户确认授权范围"></el-step>
                  <el-step title="生成授权码" description="生成临时授权码"></el-step>
                  <el-step title="重定向回调" description="重定向到应用回调地址"></el-step>
                  <el-step title="交换令牌" description="使用授权码交换访问令牌"></el-step>
                </el-steps>
              </el-card>

              <el-card class="auth-info-card" shadow="never">
                <div slot="header" class="auth-card-header">
                  <div class="auth-header-title">
                    <i class="el-icon-info"></i>
                    <span>授权信息</span>
                  </div>
                </div>
                <div class="auth-info">
                  <div class="info-item">
                    <span class="info-label">授权端点:</span>
                    <span class="info-value">https://oauth.example.com/authorize</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">令牌端点:</span>
                    <span class="info-value">https://oauth.example.com/token</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">用户信息端点:</span>
                    <span class="info-value">https://oauth.example.com/userinfo</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">撤销端点:</span>
                    <span class="info-value">https://oauth.example.com/revoke</span>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 授权历史 -->
        <div v-if="activeMenu === 'history'" class="history-section">
          <div class="section-header">
            <h3>
              <i class="el-icon-time"></i>
              授权历史
            </h3>
            <div class="section-actions">
              <el-input
                v-model="historySearch"
                placeholder="搜索授权记录"
                prefix-icon="el-icon-search"
                size="small"
                style="width: 200px"
              ></el-input>
              <el-button size="small" @click="exportHistory">
                <i class="el-icon-download"></i>导出
              </el-button>
            </div>
          </div>

          <el-card class="history-card">
            <el-table
              :data="filteredHistory"
              stripe
              :header-cell-style="{ backgroundColor: '#f8f9fa' }"
            >
              <el-table-column prop="id" label="记录ID" width="80"></el-table-column>
              <el-table-column prop="appName" label="应用名称" width="150">
                <template slot-scope="scope">
                  <div class="app-info">
                    <el-avatar :size="24" :src="scope.row.appIcon"></el-avatar>
                    <span>{{ scope.row.appName }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="user" label="用户" width="120"></el-table-column>
              <el-table-column prop="scopes" label="权限范围" width="200">
                <template slot-scope="scope">
                  <el-tag
                    v-for="scope_item in scope.row.scopes"
                    :key="scope_item"
                    size="mini"
                    class="scope-tag"
                  >
                    {{ scope_item }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'" size="mini">
                    {{ scope.row.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="授权时间" width="180"></el-table-column>
              <el-table-column prop="ip" label="IP地址" width="120"></el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button size="mini" type="text" @click="viewHistoryDetail(scope.row)">
                    详情
                  </el-button>
                  <el-button
                    size="mini"
                    type="text"
                    style="color: #f56c6c"
                    @click="revokeByHistory(scope.row)"
                  >
                    撤销
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div class="pagination-wrapper">
              <el-pagination
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalHistory"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              >
              </el-pagination>
            </div>
          </el-card>
        </div>

        <!-- 系统设置 -->
        <div v-if="activeMenu === 'settings'" class="settings-section">
          <div class="section-header">
            <h3>
              <i class="el-icon-setting"></i>
              系统设置
            </h3>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="settings-card">
                <div slot="header">
                  <i class="el-icon-lock"></i>
                  <span>安全设置</span>
                </div>
                <el-form label-width="120px">
                  <el-form-item label="令牌有效期">
                    <el-input-number
                      v-model="settings.tokenExpiry"
                      :min="1"
                      :max="86400"
                      controls-position="right"
                    ></el-input-number>
                    <span class="form-help">秒</span>
                  </el-form-item>

                  <el-form-item label="刷新令牌有效期">
                    <el-input-number
                      v-model="settings.refreshTokenExpiry"
                      :min="1"
                      :max="2592000"
                      controls-position="right"
                    ></el-input-number>
                    <span class="form-help">秒</span>
                  </el-form-item>

                  <el-form-item label="授权码有效期">
                    <el-input-number
                      v-model="settings.authCodeExpiry"
                      :min="1"
                      :max="600"
                      controls-position="right"
                    ></el-input-number>
                    <span class="form-help">秒</span>
                  </el-form-item>

                  <el-form-item label="强制HTTPS">
                    <el-switch v-model="settings.forceHttps"></el-switch>
                  </el-form-item>

                  <el-form-item label="启用PKCE">
                    <el-switch v-model="settings.enablePkce"></el-switch>
                  </el-form-item>
                </el-form>
              </el-card>
            </el-col>

            <el-col :span="12">
              <el-card class="settings-card">
                <div slot="header">
                  <i class="el-icon-notification"></i>
                  <span>通知设置</span>
                </div>
                <el-form label-width="120px">
                  <el-form-item label="邮件通知">
                    <el-switch v-model="settings.emailNotification"></el-switch>
                  </el-form-item>

                  <el-form-item label="令牌过期提醒">
                    <el-switch v-model="settings.tokenExpiryAlert"></el-switch>
                  </el-form-item>

                  <el-form-item label="异常登录提醒">
                    <el-switch v-model="settings.suspiciousActivity"></el-switch>
                  </el-form-item>

                  <el-form-item label="提醒邮箱">
                    <el-input v-model="settings.alertEmail" type="email"></el-input>
                  </el-form-item>
                </el-form>
              </el-card>

              <el-card class="settings-card">
                <div slot="header">
                  <i class="el-icon-pie-chart"></i>
                  <span>审计日志</span>
                </div>
                <el-form label-width="120px">
                  <el-form-item label="启用审计">
                    <el-switch v-model="settings.enableAudit"></el-switch>
                  </el-form-item>

                  <el-form-item label="日志保留天数">
                    <el-input-number
                      v-model="settings.logRetentionDays"
                      :min="1"
                      :max="365"
                      controls-position="right"
                    ></el-input-number>
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="saveSettings">保存设置</el-button>
                    <el-button @click="resetSettings">重置</el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-main>
    </el-container>

    <!-- 对话框 -->
    <!-- 应用表单对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="resetForm">
      <el-form ref="appForm" :model="appForm" :rules="appRules" label-width="120px">
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="appForm.name" placeholder="请输入应用名称"></el-input>
        </el-form-item>

        <el-form-item label="应用图标">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="appForm.icon" :src="appForm.icon" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>

        <el-form-item label="应用描述">
          <el-input
            v-model="appForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入应用描述"
          ></el-input>
        </el-form-item>

        <el-form-item label="回调地址" prop="redirectUri">
          <el-input
            v-model="appForm.redirectUri"
            placeholder="https://example.com/callback"
          ></el-input>
        </el-form-item>

        <el-form-item label="授权类型">
          <el-checkbox-group v-model="appForm.grantTypes">
            <el-checkbox label="authorization_code">授权码模式</el-checkbox>
            <el-checkbox label="client_credentials">客户端模式</el-checkbox>
            <el-checkbox label="refresh_token">刷新令牌</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="权限范围">
          <el-checkbox-group v-model="appForm.scopes">
            <el-checkbox v-for="scope in availableScopes" :key="scope.name" :label="scope.name">
              {{ scope.description }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="状态">
          <el-radio-group v-model="appForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="saving" @click="saveApp">
          {{ editingApp ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 权限范围对话框 -->
    <el-dialog title="权限范围管理" :visible.sync="scopeDialogVisible" width="500px">
      <el-form ref="scopeForm" :model="scopeForm" :rules="scopeRules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="scopeForm.name" placeholder="如：read"></el-input>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="scopeForm.description" placeholder="如：读取基本信息"></el-input>
        </el-form-item>

        <el-form-item label="是否默认">
          <el-switch v-model="scopeForm.isDefault"></el-switch>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="scopeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveScope">
          {{ editingScope ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 令牌详情对话框 -->
    <el-dialog title="令牌详情" :visible.sync="tokenDetailVisible" width="800px">
      <div v-if="currentToken" class="token-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="令牌ID">
            {{ currentToken.id }}
          </el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="currentToken.type === 'access' ? 'success' : 'info'">
              {{ currentToken.type === 'access' ? '访问令牌' : '刷新令牌' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="应用">
            {{ currentToken.appName }}
          </el-descriptions-item>
          <el-descriptions-item label="用户">
            {{ currentToken.user }}
          </el-descriptions-item>
          <el-descriptions-item label="权限范围">
            <el-tag v-for="scope in currentToken.scopes" :key="scope" size="mini" class="scope-tag">
              {{ scope }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentToken.status)">
              {{ getStatusText(currentToken.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ currentToken.createdAt }}
          </el-descriptions-item>
          <el-descriptions-item label="过期时间">
            {{ currentToken.expiresAt }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="token-actions">
          <el-button
            v-if="currentToken.type === 'refresh'"
            type="primary"
            size="small"
            @click="refreshToken(currentToken)"
          >
            刷新令牌
          </el-button>
          <el-button type="danger" size="small" @click="revokeToken(currentToken)">
            撤销令牌
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'OAuth2Manager',
  data() {
    return {
      // 主题
      isDark: false,

      // 菜单
      activeMenu: 'dashboard',

      // 侧边栏
      isCollapsed: false,

      // 用户信息
      userInfo: {
        username: 'Admin',
        role: '系统管理员',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      },

      // 通知
      notifications: [
        {
          id: 1,
          icon: 'el-icon-warning',
          message: '有3个令牌即将过期',
          type: 'warning',
        },
        {
          id: 2,
          icon: 'el-icon-success',
          message: '新应用审核通过',
          type: 'success',
        },
      ],

      // 统计数据
      stats: {
        totalApplications: 24,
        activeTokens: 156,
        dailyAuth: 89,
        expiredTokens: 12,
      },

      // 图表数据
      chartPeriod: '7d',
      recentActivities: [
        {
          id: 1,
          title: '新应用注册',
          description: '移动端应用已成功注册',
          timestamp: '2024-01-20 14:25:00',
          type: 'success',
        },
        {
          id: 2,
          title: '令牌刷新',
          description: 'Web管理后台刷新了访问令牌',
          timestamp: '2024-01-20 13:15:00',
          type: 'info',
        },
      ],

      // 应用数据
      applications: [
        {
          id: 1,
          name: '移动端应用',
          clientId: 'mobile_app_001',
          clientSecret: 'secret_mobile_001',
          icon: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          description: '官方移动端应用',
          redirectUri: 'https://mobile.example.com/callback',
          grantTypes: ['authorization_code', 'refresh_token'],
          scopes: ['read', 'write'],
          status: 'active',
          createdAt: '2024-01-15 10:30:00',
          lastUsed: '2024-01-20 14:25:00',
        },
        {
          id: 2,
          name: 'Web管理后台',
          clientId: 'web_admin_002',
          clientSecret: 'secret_web_002',
          icon: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          description: 'Web管理后台应用',
          redirectUri: 'https://admin.example.com/callback',
          grantTypes: ['authorization_code', 'client_credentials'],
          scopes: ['read', 'write', 'admin'],
          status: 'active',
          createdAt: '2024-01-10 09:15:00',
          lastUsed: '2024-01-20 16:45:00',
        },
      ],

      // 令牌数据
      tokens: [
        {
          id: 'at_001',
          type: 'access',
          appName: '移动端应用',
          user: 'user001',
          scopes: ['read', 'write'],
          status: 'active',
          createdAt: '2024-01-20 10:30:00',
          expiresAt: '2024-01-20 18:30:00',
          lastUsed: '2024-01-20 14:25:00',
        },
        {
          id: 'rt_001',
          type: 'refresh',
          appName: '移动端应用',
          user: 'user001',
          scopes: ['read', 'write'],
          status: 'active',
          createdAt: '2024-01-20 10:30:00',
          expiresAt: '2024-01-27 10:30:00',
          lastUsed: '2024-01-20 14:25:00',
        },
      ],

      // 权限范围
      scopes: [
        {
          id: 1,
          name: 'read',
          description: '读取基本信息',
          level: 'basic',
          isDefault: true,
          createdAt: '2024-01-01 00:00:00',
        },
        {
          id: 2,
          name: 'write',
          description: '写入和修改信息',
          level: 'standard',
          isDefault: false,
          createdAt: '2024-01-01 00:00:00',
        },
        {
          id: 3,
          name: 'admin',
          description: '管理员权限',
          level: 'advanced',
          isDefault: false,
          createdAt: '2024-01-01 00:00:00',
        },
      ],

      // 权限范围分类
      scopeCategories: [
        {
          name: '基础权限',
          icon: 'el-icon-user',
          scopes: [
            {
              id: 1,
              name: 'read',
              description: '读取基本信息',
              level: 'basic',
            },
          ],
        },
        {
          name: '标准权限',
          icon: 'el-icon-edit',
          scopes: [
            {
              id: 2,
              name: 'write',
              description: '写入和修改信息',
              level: 'standard',
            },
          ],
        },
        {
          name: '高级权限',
          icon: 'el-icon-setting',
          scopes: [
            {
              id: 3,
              name: 'admin',
              description: '管理员权限',
              level: 'advanced',
            },
          ],
        },
      ],

      // 授权历史
      authHistory: [
        {
          id: 1,
          appName: '移动端应用',
          appIcon: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          user: 'user001',
          scopes: ['read', 'write'],
          status: 'success',
          createdAt: '2024-01-20 14:25:00',
          ip: '*************',
        },
        {
          id: 2,
          appName: 'Web管理后台',
          appIcon: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          user: 'admin',
          scopes: ['read', 'write', 'admin'],
          status: 'success',
          createdAt: '2024-01-20 16:45:00',
          ip: '*************',
        },
      ],

      // 授权表单
      authForm: {
        clientId: '',
        redirectUri: '',
        scopes: [],
        responseType: 'code',
        state: '',
      },

      // 对话框
      dialogVisible: false,
      dialogTitle: '',
      showCreateApp: false,
      showCreateScope: false,
      scopeDialogVisible: false,
      tokenDetailVisible: false,

      // 表单
      appForm: {
        name: '',
        icon: '',
        description: '',
        redirectUri: '',
        grantTypes: [],
        scopes: [],
        status: 'active',
      },

      scopeForm: {
        name: '',
        description: '',
        isDefault: false,
      },

      // 当前编辑项
      editingApp: null,
      editingScope: null,
      currentToken: null,

      // 加载状态
      loading: false,
      saving: false,
      authLoading: false,

      // 授权步骤
      currentStep: 0,

      // 搜索
      searchText: '',
      tokenSearchText: '',
      scopeSearch: '',
      historySearch: '',

      // 过滤器
      filters: {
        type: '',
        status: '',
      },

      // 分页
      currentPage: 1,
      pageSize: 10,
      totalHistory: 100,

      // 系统设置
      settings: {
        tokenExpiry: 3600,
        refreshTokenExpiry: 604800,
        authCodeExpiry: 600,
        forceHttps: true,
        enablePkce: true,
        emailNotification: true,
        tokenExpiryAlert: true,
        suspiciousActivity: true,
        alertEmail: '<EMAIL>',
        enableAudit: true,
        logRetentionDays: 90,
      },

      // 表单验证规则
      appRules: {
        name: [{ required: true, message: '请输入应用名称', trigger: 'blur' }],
        redirectUri: [
          { required: true, message: '请输入回调地址', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' },
        ],
      },

      scopeRules: {
        name: [{ required: true, message: '请输入权限范围名称', trigger: 'blur' }],
        description: [{ required: true, message: '请输入权限范围描述', trigger: 'blur' }],
      },

      authRules: {
        clientId: [{ required: true, message: '请选择应用', trigger: 'change' }],
        redirectUri: [
          { required: true, message: '请输入回调地址', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' },
        ],
        scopes: [{ required: true, message: '请选择权限范围', trigger: 'change' }],
        responseType: [{ required: true, message: '请选择响应类型', trigger: 'change' }],
      },
    };
  },

  computed: {
    // 可用的权限范围
    availableScopes() {
      return this.scopes.map((scope) => ({
        name: scope.name,
        description: scope.description,
      }));
    },

    // 过滤后的应用
    filteredApplications() {
      let filtered = this.applications.filter(
        (app) =>
          app.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
          app.clientId.toLowerCase().includes(this.searchText.toLowerCase())
      );

      if (this.filters.type) {
        filtered = filtered.filter((app) => app.type === this.filters.type);
      }

      if (this.filters.status) {
        filtered = filtered.filter((app) => app.status === this.filters.status);
      }

      return filtered;
    },

    // 过滤后的令牌
    filteredTokens() {
      return this.tokens.filter(
        (token) =>
          token.appName.toLowerCase().includes(this.tokenSearchText.toLowerCase()) ||
          token.user.toLowerCase().includes(this.tokenSearchText.toLowerCase()) ||
          token.id.toLowerCase().includes(this.tokenSearchText.toLowerCase())
      );
    },

    // 过滤后的权限范围
    filteredScopes() {
      return this.scopes.filter(
        (scope) =>
          scope.name.toLowerCase().includes(this.scopeSearch.toLowerCase()) ||
          scope.description.toLowerCase().includes(this.scopeSearch.toLowerCase())
      );
    },

    // 过滤后的历史记录
    filteredHistory() {
      return this.authHistory.filter(
        (history) =>
          history.appName.toLowerCase().includes(this.historySearch.toLowerCase()) ||
          history.user.toLowerCase().includes(this.historySearch.toLowerCase())
      );
    },

    // 即将过期的令牌
    expiringTokens() {
      const now = new Date();
      const oneHour = 60 * 60 * 1000;

      return this.tokens.filter((token) => {
        const expTime = new Date(token.expiresAt);
        return expTime - now <= oneHour && expTime > now;
      });
    },

    // 令牌统计
    activeTokensCount() {
      return this.tokens.filter((token) => token.status === 'active').length;
    },

    expiredTokensCount() {
      return this.tokens.filter((token) => token.status === 'expired').length;
    },

    revokedTokensCount() {
      return this.tokens.filter((token) => token.status === 'revoked').length;
    },

    // 侧边栏宽度
    sidebarWidth() {
      return this.isCollapsed ? '64px' : '200px';
    },
  },

  mounted() {
    // 初始化主题
    document.documentElement.setAttribute('data-theme', 'light');

    // 模拟数据加载
    this.loading = true;
    setTimeout(() => {
      this.loading = false;
    }, 1000);

    // 检查即将过期的令牌
    if (this.expiringTokens.length > 0) {
      this.$notify({
        title: '令牌过期提醒',
        message: `有 ${this.expiringTokens.length} 个令牌即将过期`,
        type: 'warning',
        duration: 0,
      });
    }
  },

  methods: {
    // 获取菜单名称
    getMenuName(menu) {
      const menuMap = {
        dashboard: '仪表盘',
        applications: '应用管理',
        tokens: '令牌管理',
        scopes: '权限范围',
        authorize: '授权中心',
        history: '授权历史',
        settings: '系统设置',
        logs: '操作日志',
      };
      return menuMap[menu] || '未知页面';
    },

    // 切换侧边栏
    toggleSidebar() {
      this.isCollapsed = !this.isCollapsed;
    },

    // 菜单选择
    handleMenuSelect(index) {
      this.activeMenu = index;
    },

    // 切换主题
    toggleTheme() {
      this.isDark = !this.isDark;
      document.documentElement.setAttribute('data-theme', this.isDark ? 'dark' : 'light');
    },

    // 处理通知
    handleNotification(id) {
      this.notifications = this.notifications.filter((n) => n.id !== id);
      this.$message.success('通知已处理');
    },

    // 处理用户菜单
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$message.info('个人信息');
          break;
        case 'settings':
          this.$message.info('设置');
          break;
        case 'logout':
          this.$confirm('确定要退出登录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            this.$message.success('退出成功');
          });
          break;
      }
    },

    // 刷新数据
    refreshData() {
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
        this.$message.success('数据已刷新');
      }, 1000);
    },

    // 重置过滤器
    resetFilters() {
      this.filters = {
        type: '',
        status: '',
      };
    },

    // 应用管理
    createApp() {
      this.editingApp = null;
      this.dialogTitle = '创建应用';
      this.dialogVisible = true;
      this.resetForm();
    },

    viewApp(app) {
      this.$message.info(`查看应用: ${app.name}`);
    },

    deleteApp(app) {
      this.$confirm(`确定要删除应用 "${app.name}" 吗？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const index = this.applications.findIndex((a) => a.id === app.id);
        if (index !== -1) {
          this.applications.splice(index, 1);
          this.$message.success('应用删除成功');
        }
      });
    },

    handleAppAction(command, app) {
      switch (command) {
        case 'edit':
          this.editApp(app);
          break;
        case 'view':
          this.viewApp(app);
          break;
        case 'reset':
          this.$message.info('重置密钥功能开发中');
          break;
        case 'delete':
          this.deleteApp(app);
          break;
      }
    },

    getAppTypeTag(type) {
      const typeMap = {
        web: 'primary',
        mobile: 'success',
        desktop: 'info',
        service: 'warning',
      };
      return typeMap[type] || '';
    },

    getStatusTag(status) {
      const statusMap = {
        active: 'success',
        inactive: 'danger',
        pending: 'warning',
      };
      return statusMap[status] || '';
    },

    editApp(app) {
      this.editingApp = app;
      this.dialogTitle = '编辑应用';
      this.dialogVisible = true;
      this.appForm = { ...app };
    },

    saveApp() {
      this.$refs.appForm.validate((valid) => {
        if (valid) {
          this.saving = true;

          setTimeout(() => {
            if (this.editingApp) {
              const index = this.applications.findIndex((app) => app.id === this.editingApp.id);
              if (index !== -1) {
                this.applications[index] = { ...this.appForm, id: this.editingApp.id };
              }
              this.$message.success('应用更新成功');
            } else {
              const newApp = {
                ...this.appForm,
                id: Date.now(),
                clientId: 'app_' + Math.random().toString(36).substring(2, 11),
                clientSecret: 'secret_' + Math.random().toString(36).substring(2, 18),
                createdAt: new Date().toLocaleString(),
                lastUsed: '-',
              };
              this.applications.push(newApp);
              this.$message.success('应用创建成功');
            }

            this.saving = false;
            this.dialogVisible = false;
            this.resetForm();
          }, 1000);
        }
      });
    },

    resetForm() {
      this.appForm = {
        name: '',
        icon: '',
        description: '',
        redirectUri: '',
        grantTypes: [],
        scopes: [],
        status: 'active',
      };
      if (this.$refs.appForm) {
        this.$refs.appForm.clearValidate();
      }
    },

    // 令牌管理
    refreshAllTokens() {
      this.$message.success('所有令牌已刷新');
    },

    cleanupExpiredTokens() {
      this.$confirm('确定要清理所有过期令牌吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.tokens = this.tokens.filter((token) => token.status !== 'expired');
        this.$message.success('过期令牌已清理');
      });
    },

    tableRowClassName({ row }) {
      if (row.status === 'expired') {
        return 'warning-row';
      } else if (row.status === 'revoked') {
        return 'danger-row';
      }
      return '';
    },

    getTokenStatusTag(status) {
      const statusMap = {
        active: 'success',
        expired: 'warning',
        revoked: 'danger',
      };
      return statusMap[status] || '';
    },

    getTokenProgress(token) {
      const now = new Date();
      const created = new Date(token.createdAt);
      const expires = new Date(token.expiresAt);
      const total = expires - created;
      const elapsed = now - created;
      return Math.max(0, Math.min(100, (elapsed / total) * 100));
    },

    getTokenProgressStatus(token) {
      const progress = this.getTokenProgress(token);
      if (progress > 80) return 'exception';
      if (progress > 60) return 'warning';
      return 'success';
    },

    copyToken(token) {
      navigator.clipboard.writeText(token).then(() => {
        this.$message.success('令牌已复制到剪贴板');
      });
    },

    viewToken(token) {
      this.currentToken = token;
      this.tokenDetailVisible = true;
    },

    refreshToken(token) {
      this.$confirm('确定要刷新这个令牌吗？', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }).then(() => {
        // 模拟刷新令牌
        const newToken = {
          ...token,
          id: 'at_' + Math.random().toString(36).substring(2, 11),
          createdAt: new Date().toLocaleString(),
          expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toLocaleString(),
        };

        // 更新令牌列表
        const index = this.tokens.findIndex((t) => t.id === token.id);
        if (index !== -1) {
          this.tokens[index] = newToken;
        }

        this.currentToken = newToken;
        this.$message.success('令牌刷新成功');
      });
    },

    revokeToken(token) {
      this.$confirm('确定要撤销这个令牌吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const index = this.tokens.findIndex((t) => t.id === token.id);
        if (index !== -1) {
          this.tokens[index].status = 'revoked';
        }
        this.$message.success('令牌已撤销');
        this.tokenDetailVisible = false;
      });
    },

    batchRevokeTokens() {
      this.$confirm('确定要批量撤销选中的令牌吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 这里应该处理批量撤销逻辑
        this.$message.success('批量撤销成功');
      });
    },

    // 权限范围管理
    getScopeLevelType(level) {
      const levelMap = {
        basic: 'success',
        standard: 'warning',
        advanced: 'danger',
      };
      return levelMap[level] || '';
    },

    handleScopeAction(command, scope) {
      switch (command) {
        case 'edit':
          this.editScope(scope);
          break;
        case 'delete':
          this.deleteScope(scope);
          break;
      }
    },

    deleteScope(scope) {
      this.$confirm(`确定要删除权限范围 "${scope.name}" 吗？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const index = this.scopes.findIndex((s) => s.id === scope.id);
        if (index !== -1) {
          this.scopes.splice(index, 1);
          this.$message.success('权限范围删除成功');
        }
      });
    },

    createScope() {
      this.editingScope = null;
      this.scopeDialogVisible = true;
      this.scopeForm = {
        name: '',
        description: '',
        isDefault: false,
      };
    },

    editScope(scope) {
      this.editingScope = scope;
      this.scopeDialogVisible = true;
      this.scopeForm = { ...scope };
    },

    saveScope() {
      this.$refs.scopeForm.validate((valid) => {
        if (valid) {
          if (this.editingScope) {
            const index = this.scopes.findIndex((s) => s.id === this.editingScope.id);
            if (index !== -1) {
              this.scopes[index] = { ...this.scopeForm, id: this.editingScope.id };
            }
            this.$message.success('权限范围更新成功');
          } else {
            const newScope = {
              ...this.scopeForm,
              id: Date.now(),
              createdAt: new Date().toLocaleString(),
            };
            this.scopes.push(newScope);
            this.$message.success('权限范围创建成功');
          }

          this.scopeDialogVisible = false;
        }
      });
    },

    // 授权流程
    submitAuth() {
      this.$refs.authForm.validate((valid) => {
        if (valid) {
          this.startAuth();
        }
      });
    },

    startAuth() {
      this.authLoading = true;
      this.currentStep = 0;

      // 模拟授权流程
      const steps = [
        { message: '验证客户端信息...', delay: 1000 },
        { message: '用户确认授权...', delay: 1500 },
        { message: '生成授权码...', delay: 800 },
        { message: '重定向到回调地址...', delay: 1000 },
        { message: '授权完成', delay: 500 },
      ];

      const executeStep = (index) => {
        if (index < steps.length) {
          setTimeout(() => {
            this.currentStep = index + 1;
            this.$message.info(steps[index].message);
            executeStep(index + 1);
          }, steps[index].delay);
        } else {
          this.authLoading = false;
          this.$message.success('授权流程完成');
        }
      };

      executeStep(0);
    },

    resetAuthForm() {
      this.authForm = {
        clientId: '',
        redirectUri: '',
        scopes: [],
        responseType: 'code',
        state: '',
      };
      this.currentStep = 0;
    },

    // 授权历史
    viewHistoryDetail(history) {
      this.$alert(
        `
        <div style="text-align: left;">
          <p><strong>应用：</strong>${history.appName}</p>
          <p><strong>用户：</strong>${history.user}</p>
          <p><strong>权限范围：</strong>${history.scopes.join(', ')}</p>
          <p><strong>状态：</strong>${history.status === 'success' ? '成功' : '失败'}</p>
          <p><strong>时间：</strong>${history.createdAt}</p>
          <p><strong>IP地址：</strong>${history.ip}</p>
        </div>
      `,
        '授权详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
        }
      );
    },

    revokeByHistory(historyItem) {
      console.log('Revoking tokens for history:', historyItem);
      this.$confirm('确定要撤销此次授权相关的所有令牌吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.$message.success('相关令牌已撤销');
      });
    },

    exportHistory() {
      this.$message.info('导出历史记录...');
      // 实际项目中这里应该调用导出API
      setTimeout(() => {
        this.$message.success('导出完成');
      }, 2000);
    },

    // 分页
    handleSizeChange(size) {
      this.pageSize = size;
    },

    handleCurrentChange(page) {
      this.currentPage = page;
    },

    // 系统设置
    saveSettings() {
      this.$message.success('设置保存成功');
    },

    resetSettings() {
      this.$confirm('确定要重置所有设置吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 重置设置到默认值
        this.settings = {
          tokenExpiry: 3600,
          refreshTokenExpiry: 604800,
          authCodeExpiry: 600,
          forceHttps: true,
          enablePkce: true,
          emailNotification: true,
          tokenExpiryAlert: true,
          suspiciousActivity: true,
          alertEmail: '<EMAIL>',
          enableAudit: true,
          logRetentionDays: 90,
        };
        this.$message.success('设置已重置');
      });
    },

    // 工具方法
    getStatusType(status) {
      const statusMap = {
        active: 'success',
        expired: 'warning',
        revoked: 'danger',
        disabled: 'info',
      };
      return statusMap[status] || 'info';
    },

    getStatusText(status) {
      const statusMap = {
        active: '活跃',
        expired: '已过期',
        revoked: '已撤销',
        disabled: '已禁用',
      };
      return statusMap[status] || '未知';
    },

    copyToClipboard(text) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this.$message.success('已复制到剪贴板');
        })
        .catch(() => {
          this.$message.error('复制失败');
        });
    },

    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }

      // 模拟图片上传
      if (isJPG && isLt2M) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.appForm.icon = e.target.result;
        };
        reader.readAsDataURL(file);
      }

      return false; // 阻止默认上传行为
    },
  },
};
</script>

<style scoped>
/* 全局样式 */
.oauth-container {
  min-height: 100vh;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
}

.main-container {
  flex: 1;
  height: calc(100vh - 60px);
}

/* 主题样式 */
[data-theme='dark'] .oauth-container {
  background-color: #1a1a1a;
  color: #e0e0e0;
}

[data-theme='dark'] .el-card {
  background-color: #2d2d2d;
  border-color: #404040;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 30px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: bold;
}

.logo i {
  font-size: 24px;
}

.breadcrumb {
  color: rgba(255, 255, 255, 0.8);
}

.breadcrumb >>> .el-breadcrumb__inner {
  color: rgba(255, 255, 255, 0.8);
}

.breadcrumb >>> .el-breadcrumb__inner:hover {
  color: white;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.theme-toggle,
.notification-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.theme-toggle:hover,
.notification-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notification-badge {
  margin-right: 5px;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px 0;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-weight: bold;
  font-size: 14px;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
}

/* 侧边栏样式 */
.sidebar {
  background-color: #001529;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  border-right: none;
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar-collapsed {
  width: 64px !important;
}

.sidebar-header {
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid #1f1f1f;
}

.collapse-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  transition: all 0.3s;
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.sidebar-menu {
  border-right: none;
  background-color: #001529;
}

.sidebar-menu .menu-item {
  height: 56px;
  line-height: 56px;
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.sidebar-menu .menu-item:hover {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.sidebar-menu .menu-item.is-active {
  background-color: #1890ff;
  color: #ffffff;
}

.sidebar-menu .menu-item i {
  font-size: 18px;
  width: 24px;
  text-align: center;
  margin-right: 12px;
}

.sidebar-menu.el-menu--collapse .menu-item {
  text-align: center;
  margin: 4px;
}

/* 主内容样式 */
.main-content {
  padding: 24px;
  background-color: #f0f2f5;
  overflow-y: auto;
  height: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-header h2 {
  margin: 0;
  color: #262626;
  font-size: 20px;
  font-weight: 600;
}

.page-header i {
  margin-right: 8px;
  color: #1890ff;
}

.page-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  width: 240px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.section-header i {
  margin-right: 8px;
  color: #409eff;
}

.section-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 统计卡片样式 */
.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.stat-card.blue {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
}

.stat-card.green {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  color: white;
}

.stat-card.orange {
  background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);
  color: white;
}

.stat-card.red {
  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
  color: white;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-content {
  padding: 24px 20px;
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 12px;
}

.stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  opacity: 0.8;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 48px;
  opacity: 0.2;
  z-index: 1;
}

/* 过滤栏样式 */
.filter-bar {
  background: #ffffff;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 应用网格样式 */
.app-grid {
  margin-top: 16px;
}

.app-col {
  margin-bottom: 20px;
}

/* 应用卡片样式 */
.app-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid #e8e8e8;
  background: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.app-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #1890ff;
}

.app-card-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.app-basic-info {
  display: flex;
  gap: 12px;
  flex: 1;
}

.app-avatar {
  flex-shrink: 0;
  border: 2px solid #f0f0f0;
  transition: border-color 0.3s;
}

.app-card:hover .app-avatar {
  border-color: #1890ff;
}

.app-title-section {
  flex: 1;
  min-width: 0;
}

.app-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.app-description {
  margin: 0;
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.app-dropdown {
  flex-shrink: 0;
}

.more-btn {
  color: #8c8c8c;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s;
}

.more-btn:hover {
  background-color: #f5f5f5;
  color: #1890ff;
}

.app-card-body {
  padding: 16px 20px;
  flex: 1;
}

.app-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.app-card-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.app-actions {
  width: 100%;
}

.app-actions .el-button {
  flex: 1;
}

/* 图表区域样式 */
.charts-row {
  margin-bottom: 24px;
}

.chart-card {
  border-radius: 12px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.chart-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-controls .el-button {
  border-radius: 4px;
}

.chart-content {
  padding: 24px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #8c8c8c;
}

.chart-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
  display: block;
}

.chart-text {
  font-size: 16px;
  margin: 0 0 8px 0;
  color: #595959;
}

.chart-desc {
  font-size: 14px;
  color: #8c8c8c;
}

/* 快速操作和最近活动样式 */
.bottom-row {
  margin-bottom: 24px;
}

.quick-actions-card,
.recent-activity-card {
  border-radius: 12px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 8px 0;
}

.quick-actions .el-button {
  height: 48px;
  border-radius: 8px;
  font-weight: 500;
}

.activity-timeline {
  margin-top: 16px;
}

.activity-content {
  padding-left: 8px;
}

.activity-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.activity-description {
  font-size: 13px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 令牌概览样式 */
.token-overview {
  margin-bottom: 24px;
}

.token-summary-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
}

.token-summary-card.active {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  color: white;
}

.token-summary-card.expired {
  background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
  color: white;
}

.token-summary-card.revoked {
  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
  color: white;
}

.token-summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.token-summary {
  display: flex;
  align-items: center;
  padding: 24px 20px;
  position: relative;
}

.token-summary .summary-icon {
  font-size: 36px;
  margin-right: 16px;
  opacity: 0.9;
}

.token-summary .summary-content {
  flex: 1;
}

.token-summary .summary-number {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
}

.token-summary .summary-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 令牌表格样式 */
.token-table-card {
  border-radius: 12px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.table-header span {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.expires-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.token-progress {
  width: 100%;
}

.scope-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

/* 权限范围分类样式 */
.scope-categories {
  margin-top: 16px;
}

.scope-category-card {
  border-radius: 12px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  height: 100%;
}

.scope-category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #1890ff;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-icon {
  font-size: 18px;
  color: #1890ff;
}

.category-header span {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.scope-list {
  padding: 16px 20px;
}

.scope-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.scope-item:last-child {
  border-bottom: none;
}

.scope-item:hover {
  background-color: #f8f9fa;
  margin: 0 -12px;
  padding: 12px;
  border-radius: 6px;
}

.scope-info {
  flex: 1;
  min-width: 0;
}

.scope-name {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.scope-description {
  font-size: 13px;
  color: #8c8c8c;
  line-height: 1.4;
}

.scope-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.scope-more-btn {
  color: #8c8c8c;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s;
}

.scope-more-btn:hover {
  background-color: #f5f5f5;
  color: #1890ff;
}

/* 授权中心样式 */
.auth-form-card,
.auth-flow-card,
.auth-info-card {
  border-radius: 12px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 20px;
}

.auth-card-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.auth-header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.auth-header-title i {
  font-size: 18px;
  color: #1890ff;
}

.auth-header-title span {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.auth-steps {
  padding: 20px 0;
}

.auth-info {
  padding: 8px 0;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  min-width: 120px;
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 500;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #262626;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background-color: #f5f5f5;
  padding: 6px 12px;
  border-radius: 6px;
  word-break: break-all;
  border: 1px solid #e8e8e8;
}

/* 权限范围复选框样式 */
.scope-checkbox {
  width: 100%;
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.scope-checkbox:hover {
  border-color: #1890ff;
  background-color: #f0f9ff;
}

.scope-checkbox-content {
  margin-left: 8px;
}

.scope-checkbox-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.scope-checkbox-desc {
  font-size: 13px;
  color: #8c8c8c;
  line-height: 1.4;
}

.app-header {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border-bottom: 1px solid #f0f2f5;
}

.app-avatar {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.app-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.app-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.app-status {
  margin-left: auto;
}

.app-body {
  padding: 20px;
}

.app-detail {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f2f5;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  color: #909399;
  font-size: 14px;
  min-width: 80px;
}

.detail-value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

.secret-field {
  display: flex;
  align-items: center;
  gap: 8px;
}

.secret-text {
  font-family: monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.copy-btn {
  padding: 4px 8px;
  font-size: 12px;
}

.scope-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.app-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.app-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.meta-item {
  font-size: 12px;
  color: #909399;
}

.app-actions {
  display: flex;
  gap: 8px;
}

/* 令牌表格样式 */
.token-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.token-table >>> .el-table__header {
  background: #f8f9fa;
}

.token-table >>> .el-table__header th {
  background: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

.token-table >>> .el-table__row:hover {
  background: #f0f9ff;
}

.token-actions {
  display: flex;
  gap: 8px;
  margin-top: 20px;
}

/* 权限范围卡片样式 */
.scope-card {
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.scope-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.scope-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f2f5;
}

.scope-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.scope-body {
  padding: 20px;
}

.scope-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
}

.scope-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.scope-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

/* 授权流程样式 */
.auth-form-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.auth-flow-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.auth-info-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
}

.auth-steps {
  margin-top: 20px;
}

.auth-steps >>> .el-step__title {
  font-size: 14px;
  font-weight: 600;
}

.auth-steps >>> .el-step__description {
  font-size: 12px;
  color: #909399;
}

.auth-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f2f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  min-width: 100px;
  color: #909399;
  font-size: 14px;
}

.info-value {
  color: #303133;
  font-size: 14px;
  font-family: monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  word-break: break-all;
}

/* 历史记录样式 */
.history-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.app-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 设置页面样式 */
.settings-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.form-help {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
}

.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
}

.token-detail {
  padding: 20px 0;
}

.token-detail >>> .el-descriptions__body {
  background: #f8f9fa;
}

.token-detail >>> .el-descriptions-item__label {
  font-weight: 600;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .app-info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .header-left {
    gap: 12px;
  }

  .logo span {
    display: none;
  }

  .breadcrumb {
    display: none;
  }

  .user-info-text {
    display: none;
  }

  .main-content {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
  }

  .page-actions {
    width: 100%;
    flex-direction: column;
    gap: 8px;
  }

  .search-input {
    width: 100%;
  }

  .filter-bar {
    padding: 16px;
  }

  .filter-bar .el-form {
    flex-direction: column;
  }

  .filter-bar .el-form-item {
    margin-bottom: 12px;
    margin-right: 0;
  }

  .app-card-header {
    padding: 16px;
  }

  .app-basic-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .app-dropdown {
    position: absolute;
    top: 16px;
    right: 16px;
  }

  .app-card-body {
    padding: 16px;
  }

  .app-info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .app-card-footer {
    padding: 12px 16px;
  }

  .app-actions {
    flex-direction: column;
  }

  .app-actions .el-button {
    width: 100%;
    margin: 0 0 8px 0;
  }

  .token-summary {
    padding: 20px 16px;
  }

  .chart-content {
    padding: 20px 16px;
    min-height: 200px;
  }

  .scope-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .scope-actions {
    align-self: flex-end;
  }

  .auth-form-card,
  .auth-flow-card,
  .auth-info-card {
    margin-bottom: 16px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .info-label {
    min-width: auto;
  }

  .info-value {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .sidebar {
    position: fixed;
    z-index: 1000;
    height: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-content {
    padding: 12px;
  }

  .page-header {
    padding: 12px;
  }

  .stat-content {
    padding: 16px 12px;
  }

  .stat-number {
    font-size: 24px;
  }

  .chart-content {
    padding: 16px 12px;
    min-height: 150px;
  }

  .app-card-header {
    padding: 12px;
  }

  .app-card-body {
    padding: 12px;
  }

  .token-summary {
    padding: 16px 12px;
  }

  .scope-item {
    padding: 8px 0;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.oauth-container {
  animation: fadeIn 0.5s ease-out;
}

.stat-card,
.app-card,
.scope-card {
  animation: fadeIn 0.5s ease-out;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 美化滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>
