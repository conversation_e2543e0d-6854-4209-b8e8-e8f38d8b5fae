<template>
  <div class="comment-item">
    <div class="comment-header">
      <el-avatar :size="30" :src="comment.userAvatar"></el-avatar>
      <div class="comment-content">
        <div class="comment-meta">
          <span class="comment-author">{{ comment.userName }}</span>
          <span v-if="comment.replyTo" class="reply-to">
            回复 <span class="mention">@{{ comment.replyTo.userName }}</span>
          </span>
        </div>
        <div class="comment-text" v-html="formatContent(comment.content)"></div>
        <div class="comment-actions">
          <span class="comment-time">{{ formatTime(comment.timestamp) }}</span>
          <el-button
            type="text"
            size="mini"
            @click="$emit('reply', { userId: comment.userId, userName: comment.userName })"
          >
            回复
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { emojiMap } from '../data/mockData.js';

export default {
  name: 'CommentItem',
  props: {
    comment: {
      type: Object,
      required: true,
    },
    users: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    formatTime(timestamp) {
      const now = new Date();
      const time = new Date(timestamp);
      const diff = now - time;

      if (diff < 60000) return '刚刚';
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
      return `${Math.floor(diff / 86400000)}天前`;
    },
    formatContent(content) {
      let formatted = content;

      // 处理@用户
      formatted = formatted.replace(/@(\w+)/g, (match, username) => {
        const user = this.users.find((u) => u.name === username);
        if (user) {
          return `<span class="mention">@${username}</span>`;
        }
        return match;
      });

      // 处理表情包
      Object.keys(emojiMap).forEach((key) => {
        const regex = new RegExp(
          `\

$$
${key}\
$$

`,
          'g'
        );
        formatted = formatted.replace(regex, `<span class="emoji">${emojiMap[key]}</span>`);
      });

      return formatted;
    },
  },
};
</script>

<style scoped>
.comment-item {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
  background: #f8f9fa;
}

.comment-header {
  display: flex;
  align-items: flex-start;
}

.comment-content {
  margin-left: 10px;
  flex: 1;
}

.comment-meta {
  margin-bottom: 5px;
}

.comment-author {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.reply-to {
  margin-left: 5px;
  font-size: 12px;
  color: #666;
}

.comment-text {
  margin-bottom: 8px;
  line-height: 1.4;
  color: #333;
  word-wrap: break-word;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

:deep(.mention) {
  color: #409eff;
  font-weight: bold;
}

:deep(.emoji) {
  font-size: 16px;
  vertical-align: middle;
}
</style>
