<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="80%"
    :show-close="false"
    :modal="true"
    :close-on-click-modal="true"
    class="image-preview-dialog"
  >
    <div class="image-preview-container">
      <!-- 关闭按钮 -->
      <div class="close-btn" @click="dialogVisible = false">
        <i class="el-icon-close"></i>
      </div>

      <!-- 图片展示区 -->
      <div class="image-display">
        <div class="image-wrapper">
          <img
            :src="currentImage"
            :alt="`图片 ${currentIndex + 1}`"
            class="preview-image"
            @load="onImageLoad"
            @error="onImageError"
          />
        </div>

        <!-- 左右切换按钮 -->
        <div v-if="images.length > 1" class="nav-buttons">
          <div
            class="nav-btn prev-btn"
            :class="{ disabled: currentIndex === 0 }"
            @click="prevImage"
          >
            <i class="el-icon-arrow-left"></i>
          </div>
          <div
            class="nav-btn next-btn"
            :class="{ disabled: currentIndex === images.length - 1 }"
            @click="nextImage"
          >
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>

      <!-- 底部信息栏 -->
      <div class="image-info">
        <div class="image-counter">{{ currentIndex + 1 }} / {{ images.length }}</div>
        <div class="image-actions">
          <el-button type="text" class="action-btn" @click="downloadImage">
            <i class="el-icon-download"></i> 下载
          </el-button>
          <el-button type="text" class="action-btn" @click="rotateImage">
            <i class="el-icon-refresh"></i> 旋转
          </el-button>
          <el-button type="text" class="action-btn" @click="zoomIn">
            <i class="el-icon-zoom-in"></i> 放大
          </el-button>
          <el-button type="text" class="action-btn" @click="zoomOut">
            <i class="el-icon-zoom-out"></i> 缩小
          </el-button>
          <el-button type="text" class="action-btn" @click="resetImage">
            <i class="el-icon-refresh-left"></i> 重置
          </el-button>
        </div>
      </div>

      <!-- 缩略图栏 -->
      <div v-if="images.length > 1" class="thumbnail-bar">
        <div
          v-for="(image, index) in images"
          :key="index"
          class="thumbnail-item"
          :class="{ active: index === currentIndex }"
          @click="setCurrentIndex(index)"
        >
          <img :src="image" :alt="`缩略图 ${index + 1}`" />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ImagePreview',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    images: {
      type: Array,
      default: () => [],
    },
    initialIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      currentIndex: 0,
      rotation: 0,
      scale: 1,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
    currentImage() {
      return this.images[this.currentIndex] || '';
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.currentIndex = this.initialIndex;
        this.resetImage();
        document.addEventListener('keydown', this.handleKeydown);
      } else {
        document.removeEventListener('keydown', this.handleKeydown);
      }
    },
    initialIndex(val) {
      this.currentIndex = val;
    },
  },
  methods: {
    prevImage() {
      if (this.currentIndex > 0) {
        this.currentIndex--;
        this.resetImage();
      }
    },
    nextImage() {
      if (this.currentIndex < this.images.length - 1) {
        this.currentIndex++;
        this.resetImage();
      }
    },
    setCurrentIndex(index) {
      this.currentIndex = index;
      this.resetImage();
    },
    rotateImage() {
      this.rotation += 90;
      this.updateImageTransform();
    },
    zoomIn() {
      this.scale = Math.min(this.scale * 1.2, 5);
      this.updateImageTransform();
    },
    zoomOut() {
      this.scale = Math.max(this.scale / 1.2, 0.1);
      this.updateImageTransform();
    },
    resetImage() {
      this.rotation = 0;
      this.scale = 1;
      this.updateImageTransform();
    },
    updateImageTransform() {
      const image = this.$el.querySelector('.preview-image');
      if (image) {
        image.style.transform = `rotate(${this.rotation}deg) scale(${this.scale})`;
      }
    },
    downloadImage() {
      const link = document.createElement('a');
      link.href = this.currentImage;
      link.download = `image_${this.currentIndex + 1}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    onImageLoad() {
      this.updateImageTransform();
    },
    onImageError() {
      this.$message.error('图片加载失败');
    },
    handleKeydown(event) {
      switch (event.key) {
        case 'ArrowLeft':
          this.prevImage();
          break;
        case 'ArrowRight':
          this.nextImage();
          break;
        case 'Escape':
          this.dialogVisible = false;
          break;
        case '+':
        case '=':
          this.zoomIn();
          break;
        case '-':
          this.zoomOut();
          break;
        case 'r':
        case 'R':
          this.rotateImage();
          break;
      }
    },
  },
};
</script>

<style scoped>
.image-preview-dialog {
  :deep(.el-dialog) {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
  }

  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.image-preview-container {
  position: relative;
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.close-btn i {
  font-size: 20px;
  color: white;
}

.image-display {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-wrapper {
  max-width: 100%;
  max-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  cursor: grab;
}

.preview-image:active {
  cursor: grabbing;
}

.nav-buttons {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
}

.nav-btn {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  pointer-events: auto;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.nav-btn.disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-btn i {
  font-size: 24px;
  color: white;
}

.prev-btn {
  margin-left: 20px;
}

.next-btn {
  margin-right: 20px;
}

.image-info {
  background: rgba(0, 0, 0, 0.7);
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-counter {
  color: white;
  font-size: 14px;
}

.image-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  color: white !important;
  font-size: 14px;
}

.thumbnail-bar {
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  display: flex;
  justify-content: center;
  gap: 10px;
  overflow-x: auto;
}

.thumbnail-item {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s;
}

.thumbnail-item.active {
  border-color: #409eff;
}

.thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-item:hover {
  transform: scale(1.1);
}
</style>
