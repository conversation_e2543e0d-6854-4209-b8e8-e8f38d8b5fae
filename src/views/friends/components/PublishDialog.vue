<template>
  <el-dialog
    title="发布动态"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <div class="publish-form">
      <!-- 文本输入区 -->
      <div class="text-input-section">
        <el-input
          ref="textInput"
          v-model="formData.content"
          type="textarea"
          :rows="4"
          placeholder="分享新鲜事..."
          @input="handleTextInput"
          @keyup.native="handleKeyup"
        ></el-input>

        <!-- @ 用户选择下拉框 -->
        <div v-if="showMentionList" class="mention-dropdown">
          <div
            v-for="user in filteredUsers"
            :key="user.id"
            class="mention-item"
            @click="selectUser(user)"
          >
            <el-avatar :size="24" :src="user.avatar"></el-avatar>
            <span class="mention-name">{{ user.name }}</span>
          </div>
        </div>
      </div>

      <!-- 图片上传区 -->
      <div class="image-upload-section">
        <el-upload
          ref="imageUpload"
          :file-list="imageList"
          :on-preview="previewImage"
          :on-remove="removeImage"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          list-type="picture-card"
          :limit="9"
          multiple
          accept="image/*"
          action="#"
          :auto-upload="false"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </div>

      <!-- 位置选择 -->
      <div class="location-section">
        <el-input
          v-model="formData.location"
          placeholder="添加位置"
          prefix-icon="el-icon-location"
          clearable
        >
          <el-button slot="append" @click="showLocationList = !showLocationList"> 选择 </el-button>
        </el-input>

        <div v-if="showLocationList" class="location-dropdown">
          <div
            v-for="location in commonLocations"
            :key="location"
            class="location-item"
            @click="selectLocation(location)"
          >
            <i class="el-icon-location"></i>
            <span>{{ location }}</span>
          </div>
        </div>
      </div>

      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="text" class="toolbar-btn" @click="showEmojiPanel = !showEmojiPanel">
            😊 表情
          </el-button>
          <el-button type="text" class="toolbar-btn" @click="insertMention"> @ 提及 </el-button>
          <el-button type="text" class="toolbar-btn" @click="$refs.imageUpload.$refs.input.click()">
            📷 图片
          </el-button>
          <el-button type="text" class="toolbar-btn" @click="showLocationList = !showLocationList">
            📍 位置
          </el-button>
        </div>

        <div class="toolbar-right">
          <span class="char-counter" :class="{ 'over-limit': charCount > 500 }">
            {{ charCount }}/500
          </span>
        </div>
      </div>

      <!-- 表情面板 -->
      <div v-if="showEmojiPanel" class="emoji-panel">
        <div class="emoji-categories">
          <el-tabs v-model="activeEmojiTab" type="card">
            <el-tab-pane label="😊" name="smile">
              <div class="emoji-grid">
                <span
                  v-for="(emoji, key) in smileEmojis"
                  :key="key"
                  class="emoji-item"
                  @click="insertEmoji(key)"
                >
                  {{ emoji }}
                </span>
              </div>
            </el-tab-pane>
            <el-tab-pane label="❤️" name="love">
              <div class="emoji-grid">
                <span
                  v-for="(emoji, key) in loveEmojis"
                  :key="key"
                  class="emoji-item"
                  @click="insertEmoji(key)"
                >
                  {{ emoji }}
                </span>
              </div>
            </el-tab-pane>
            <el-tab-pane label="🎉" name="party">
              <div class="emoji-grid">
                <span
                  v-for="(emoji, key) in partyEmojis"
                  :key="key"
                  class="emoji-item"
                  @click="insertEmoji(key)"
                >
                  {{ emoji }}
                </span>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        :disabled="!canPublish"
        :loading="publishing"
        @click="handlePublish"
      >
        发布
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { emojiMap } from '../data/mockData.js';

export default {
  name: 'PublishDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    users: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      formData: {
        content: '',
        location: '',
      },
      imageList: [],
      showMentionList: false,
      showLocationList: false,
      showEmojiPanel: false,
      activeEmojiTab: 'smile',
      mentionQuery: '',
      cursorPosition: 0,
      publishing: false,
      commonLocations: [
        '北京市',
        '上海市',
        '广州市',
        '深圳市',
        '杭州市',
        '南京市',
        '成都市',
        '武汉市',
        '西安市',
        '重庆市',
      ],
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
    charCount() {
      return this.formData.content.length;
    },
    canPublish() {
      return (this.formData.content.trim() || this.imageList.length > 0) && this.charCount <= 500;
    },
    filteredUsers() {
      if (!this.mentionQuery) return this.users.slice(0, 5);
      return this.users
        .filter((user) => user.name.toLowerCase().includes(this.mentionQuery.toLowerCase()))
        .slice(0, 5);
    },
    smileEmojis() {
      const smileKeys = ['smile', 'laugh', 'happy', 'wink', 'cool', 'think', 'cry', 'angry'];
      return Object.fromEntries(
        smileKeys.map((key) => [key, emojiMap[key]]).filter(([, value]) => value)
      );
    },
    loveEmojis() {
      const loveKeys = ['love', 'heart', 'kiss', 'hug', 'shy', 'blush'];
      return Object.fromEntries(
        loveKeys.map((key) => [key, emojiMap[key]]).filter(([, value]) => value)
      );
    },
    partyEmojis() {
      const partyKeys = ['party', 'celebrate', 'cheer', 'dance', 'music', 'gift'];
      return Object.fromEntries(
        partyKeys.map((key) => [key, emojiMap[key]]).filter(([, value]) => value)
      );
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.textInput.focus();
        });
        document.addEventListener('click', this.handleClickOutside);
      } else {
        document.removeEventListener('click', this.handleClickOutside);
      }
    },
  },
  methods: {
    handleTextInput(value) {
      this.checkMention(value);
    },
    handleKeyup(event) {
      this.cursorPosition = event.target.selectionStart;
    },
    checkMention(value) {
      const cursorPos = this.cursorPosition;
      const beforeCursor = value.substring(0, cursorPos);
      const atIndex = beforeCursor.lastIndexOf('@');

      if (atIndex !== -1) {
        const afterAt = beforeCursor.substring(atIndex + 1);
        if (!/\s/.test(afterAt)) {
          this.mentionQuery = afterAt;
          this.showMentionList = true;
          return;
        }
      }

      this.showMentionList = false;
      this.mentionQuery = '';
    },
    selectUser(user) {
      const cursorPos = this.cursorPosition;
      const beforeCursor = this.formData.content.substring(0, cursorPos);
      const afterCursor = this.formData.content.substring(cursorPos);
      const atIndex = beforeCursor.lastIndexOf('@');

      if (atIndex !== -1) {
        const newValue = beforeCursor.substring(0, atIndex) + `@${user.name} ` + afterCursor;
        this.formData.content = newValue;
        this.showMentionList = false;
        this.mentionQuery = '';

        this.$nextTick(() => {
          const newCursorPos = atIndex + user.name.length + 2;
          this.$refs.textInput.$refs.textarea.setSelectionRange(newCursorPos, newCursorPos);
          this.$refs.textInput.focus();
        });
      }
    },
    insertMention() {
      const cursorPos = this.cursorPosition;
      const beforeCursor = this.formData.content.substring(0, cursorPos);
      const afterCursor = this.formData.content.substring(cursorPos);

      this.formData.content = beforeCursor + '@' + afterCursor;
      this.showMentionList = true;

      this.$nextTick(() => {
        const newCursorPos = cursorPos + 1;
        this.$refs.textInput.$refs.textarea.setSelectionRange(newCursorPos, newCursorPos);
        this.$refs.textInput.focus();
      });
    },
    insertEmoji(emojiKey) {
      const cursorPos = this.cursorPosition;
      const beforeCursor = this.formData.content.substring(0, cursorPos);
      const afterCursor = this.formData.content.substring(cursorPos);

      this.formData.content = beforeCursor + `[${emojiKey}]` + afterCursor;
      this.showEmojiPanel = false;

      this.$nextTick(() => {
        const newCursorPos = cursorPos + emojiKey.length + 2;
        this.$refs.textInput.$refs.textarea.setSelectionRange(newCursorPos, newCursorPos);
        this.$refs.textInput.focus();
      });
    },
    selectLocation(location) {
      this.formData.location = location;
      this.showLocationList = false;
    },
    previewImage(file) {
      // 预览图片
      const reader = new FileReader();
      reader.onload = (e) => {
        this.$alert(
          `<img src="${e.target.result}" style="max-width: 100%; max-height: 400px;">`,
          '图片预览',
          {
            dangerouslyUseHTMLString: true,
          }
        );
      };
      reader.readAsDataURL(file.raw || file);
    },
    removeImage(file, fileList) {
      this.imageList = fileList;
    },
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/');
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        return false;
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过 5MB!');
        return false;
      }

      // 创建预览URL
      const reader = new FileReader();
      reader.onload = (e) => {
        this.imageList.push({
          name: file.name,
          url: e.target.result,
          raw: file,
        });
      };
      reader.readAsDataURL(file);

      return false; // 阻止自动上传
    },
    handleUploadSuccess(response, file, fileList) {
      this.imageList = fileList;
    },
    handleUploadError(err, file, fileList) {
      this.$message.error('图片上传失败');
    },
    handlePublish() {
      if (!this.canPublish) return;

      this.publishing = true;

      // 模拟发布延迟
      setTimeout(() => {
        const images = this.imageList.map((item) => item.url);

        this.$emit('publish', {
          content: this.formData.content,
          images,
          location: this.formData.location,
        });

        this.resetForm();
        this.publishing = false;
      }, 1000);
    },
    handleClose() {
      if (this.formData.content.trim() || this.imageList.length > 0) {
        this.$confirm('确定要取消发布吗？内容将不会保存。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.resetForm();
            this.dialogVisible = false;
          })
          .catch(() => {});
      } else {
        this.resetForm();
        this.dialogVisible = false;
      }
    },
    resetForm() {
      this.formData = {
        content: '',
        location: '',
      };
      this.imageList = [];
      this.showMentionList = false;
      this.showLocationList = false;
      this.showEmojiPanel = false;
      this.mentionQuery = '';
      this.cursorPosition = 0;
    },
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.showMentionList = false;
        this.showLocationList = false;
        this.showEmojiPanel = false;
      }
    },
  },
};
</script>

<style scoped>
.publish-form {
  padding: 20px 0;
}

.text-input-section {
  position: relative;
  margin-bottom: 20px;
}

.mention-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.mention-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.mention-item:hover {
  background-color: #f5f7fa;
}

.mention-name {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}

.image-upload-section {
  margin-bottom: 20px;
}

.location-section {
  position: relative;
  margin-bottom: 20px;
}

.location-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.location-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.location-item:hover {
  background-color: #f5f7fa;
}

.location-item i {
  margin-right: 8px;
  color: #666;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-top: 1px solid #f0f0f0;
}

.toolbar-left {
  display: flex;
  gap: 20px;
}

.toolbar-btn {
  color: #666;
  padding: 5px 10px;
}

.char-counter {
  font-size: 12px;
  color: #999;
}

.char-counter.over-limit {
  color: #f56c6c;
}

.emoji-panel {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-item {
  font-size: 24px;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-item:hover {
  background-color: #f5f7fa;
  transform: scale(1.2);
}

.dialog-footer {
  text-align: right;
}

:deep(.el-upload--picture-card) {
  width: 80px;
  height: 80px;
  line-height: 80px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 80px;
  height: 80px;
}
</style>
