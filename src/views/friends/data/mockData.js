export const currentUser = {
  id: 1,
  name: '张三',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  signature: '今天也要开心呀！🌈',
};

export const allUsers = [
  currentUser,
  {
    id: 2,
    name: '李四',
    avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
    signature: '生活就像一盒巧克力',
  },
  {
    id: 3,
    name: '王五',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    signature: '做最好的自己',
  },
  {
    id: 4,
    name: '赵六',
    avatar: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    signature: '热爱生活，热爱工作',
  },
  {
    id: 5,
    name: '孙七',
    avatar: 'https://cube.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg',
    signature: '每天都是新的开始',
  },
  {
    id: 6,
    name: '周八',
    avatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    signature: '保持好奇心',
  },
];

export const emojiMap = {
  // 笑脸类
  smile: '😊',
  laugh: '😂',
  happy: '😄',
  wink: '😉',
  cool: '😎',
  think: '🤔',
  cry: '😢',
  angry: '😠',

  // 爱心类
  love: '😍',
  heart: '❤️',
  kiss: '😘',
  hug: '🤗',
  shy: '😊',
  blush: '😊',

  // 庆祝类
  party: '🎉',
  celebrate: '🍾',
  cheer: '🥳',
  dance: '💃',
  music: '🎵',
  gift: '🎁',

  // 其他
  fire: '🔥',
  star: '⭐',
  good: '👍',
  ok: '👌',
  clap: '👏',
  pray: '🙏',
  rose: '🌹',
  sun: '☀️',
  moon: '🌙',
  rainbow: '🌈',
};

export const mockPosts = [
  {
    id: 1,
    userId: 2,
    userName: '李四',
    userAvatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
    content: '今天天气真好[sun]，和@张三 一起去公园散步了，心情特别棒[happy]！',
    images: [
      'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
      'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg',
    ],
    location: '人民公园',
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    likes: [
      { userId: 1, userName: '张三', timestamp: new Date().toISOString() },
      { userId: 3, userName: '王五', timestamp: new Date().toISOString() },
    ],
    comments: [
      {
        id: 1,
        userId: 1,
        userName: '张三',
        userAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        content: '是的呢，今天天气超好[smile]！',
        timestamp: new Date(Date.now() - 1800000).toISOString(),
        replyTo: null,
      },
      {
        id: 2,
        userId: 3,
        userName: '王五',
        userAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        content: '回复 @张三，我也想去公园了[cry]',
        timestamp: new Date(Date.now() - 1200000).toISOString(),
        replyTo: { userId: 1, userName: '张三' },
      },
    ],
  },
  {
    id: 2,
    userId: 3,
    userName: '王五',
    userAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    content: '刚做的蛋糕出炉了[party]！第一次尝试，感觉还不错[good]。感谢@李四 提供的食谱[heart]',
    images: [
      'https://fuss10.elemecdn.com/9/bb/e27858e973f5d7d3904835f46abbdjpeg.jpeg',
      'https://fuss10.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg',
      'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg',
    ],
    location: '家',
    timestamp: new Date(Date.now() - 7200000).toISOString(),
    likes: [
      { userId: 1, userName: '张三', timestamp: new Date().toISOString() },
      { userId: 2, userName: '李四', timestamp: new Date().toISOString() },
      { userId: 4, userName: '赵六', timestamp: new Date().toISOString() },
    ],
    comments: [
      {
        id: 3,
        userId: 2,
        userName: '李四',
        userAvatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
        content: '哇！看起来好好吃[love]！',
        timestamp: new Date(Date.now() - 6000000).toISOString(),
        replyTo: null,
      },
      {
        id: 4,
        userId: 4,
        userName: '赵六',
        userAvatar: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
        content: '回复 @李四，什么时候请我们尝尝？[wink]',
        timestamp: new Date(Date.now() - 5400000).toISOString(),
        replyTo: { userId: 2, userName: '李四' },
      },
    ],
  },
  {
    id: 3,
    userId: 4,
    userName: '赵六',
    userAvatar: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    content:
      '周末加班中[cry]，不过项目终于要完成了[fire]！感谢团队小伙伴们的支持，特别是@张三 和@王五 的帮助[clap]',
    images: ['https://fuss10.elemecdn.com/2/11/6535bcfb26e4c79b48ddde44f4b6fjpeg.jpeg'],
    location: '公司',
    timestamp: new Date(Date.now() - 14400000).toISOString(),
    likes: [
      { userId: 1, userName: '张三', timestamp: new Date().toISOString() },
      { userId: 3, userName: '王五', timestamp: new Date().toISOString() },
    ],
    comments: [
      {
        id: 5,
        userId: 1,
        userName: '张三',
        userAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        content: '辛苦了[good]！项目完成记得庆祝一下[party]',
        timestamp: new Date(Date.now() - 10800000).toISOString(),
        replyTo: null,
      },
    ],
  },
  {
    id: 4,
    userId: 5,
    userName: '孙七',
    userAvatar: 'https://cube.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg',
    content: '今天的夕阳特别美[sun]，分享给大家[heart]。生活中的小美好总是让人心情愉悦[rainbow]',
    images: [
      'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg',
      'https://fuss10.elemecdn.com/0/6f/e35ff375812e6b0020b6b4e8f9583jpeg.jpeg',
    ],
    location: '海边',
    timestamp: new Date(Date.now() - 21600000).toISOString(),
    likes: [
      { userId: 1, userName: '张三', timestamp: new Date().toISOString() },
      { userId: 2, userName: '李四', timestamp: new Date().toISOString() },
      { userId: 6, userName: '周八', timestamp: new Date().toISOString() },
    ],
    comments: [
      {
        id: 6,
        userId: 6,
        userName: '周八',
        userAvatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        content: '太美了[star]！哪里拍的？',
        timestamp: new Date(Date.now() - 18000000).toISOString(),
        replyTo: null,
      },
      {
        id: 7,
        userId: 5,
        userName: '孙七',
        userAvatar: 'https://cube.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg',
        content: '回复 @周八，在海边拍的，有时间可以一起去[wink]',
        timestamp: new Date(Date.now() - 17400000).toISOString(),
        replyTo: { userId: 6, userName: '周八' },
      },
    ],
  },
  {
    id: 5,
    userId: 6,
    userName: '周八',
    userAvatar: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    content: '新学会了一首歌[music]，分享给大家听听[happy]。感谢@张三 推荐的吉他教程[pray]',
    images: ['https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg'],
    location: '音乐室',
    timestamp: new Date(Date.now() - 28800000).toISOString(),
    likes: [
      { userId: 1, userName: '张三', timestamp: new Date().toISOString() },
      { userId: 5, userName: '孙七', timestamp: new Date().toISOString() },
    ],
    comments: [
      {
        id: 8,
        userId: 1,
        userName: '张三',
        userAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        content: '进步很快呢[good]！继续加油[fire]',
        timestamp: new Date(Date.now() - 25200000).toISOString(),
        replyTo: null,
      },
    ],
  },
];
