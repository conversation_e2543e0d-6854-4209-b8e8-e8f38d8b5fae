<template>
  <div id="app">
    <FriendCircle />
  </div>
</template>

<script>
import FriendCircle from './components/FriendCircle.vue';

export default {
  name: 'App',
  components: {
    FriendCircle,
  },
};
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

/* 全局样式 */
.mention {
  color: #409eff;
  font-weight: bold;
  cursor: pointer;
  text-decoration: none;
}

.mention:hover {
  text-decoration: underline;
}

/* 表情符号样式 */
.emoji {
  font-size: 1.2em;
  margin: 0 1px;
  vertical-align: middle;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .friend-circle {
    padding: 10px;
  }

  .post-card {
    margin-bottom: 15px;
  }

  .publish-dialog :deep(.el-dialog) {
    width: 95% !important;
  }

  .image-preview-dialog :deep(.el-dialog) {
    width: 95% !important;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-enter-active {
  animation: fadeIn 0.3s ease-out;
}

.fade-leave-active {
  animation: fadeIn 0.3s ease-out reverse;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 点赞动画 */
@keyframes likeAnimation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.like-animation {
  animation: likeAnimation 0.3s ease-out;
}
</style>
